🇷🇺 [RU]:
Название: Weather Plugin
Автор: @example_developer
Описание: Плагин для получения актуальной информации о погоде в любом городе мира. Использует безопасный API wttr.in для получения данных о температуре, влажности, скорости ветра и описании погодных условий.
Использование: .weather [город] - получить погоду для указанного города. Если город не указан, используется город по умолчанию из настроек.
Настройки: ✅ Можно настроить город по умолчанию
Обновлено: 09.08.2025

🇺🇸 [EN]:
Name: Weather Plugin
Author: @example_developer
Description: Plugin for getting current weather information for any city in the world. Uses secure wttr.in API to fetch data about temperature, humidity, wind speed and weather conditions description.
Usage: .weather [city] - get weather for specified city. If no city is provided, uses default city from settings.
Settings: ✅ Default city can be configured
Updated: 09.08.2025
