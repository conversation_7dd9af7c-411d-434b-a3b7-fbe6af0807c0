import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List
import json
import os

logger = logging.getLogger(__name__)

class BotMonitoring:
    """Система мониторинга бота"""
    
    def __init__(self):
        self.stats = {
            'start_time': datetime.now(),
            'processed_plugins': 0,
            'approved_plugins': 0,
            'rejected_plugins': 0,
            'errors': 0,
            'api_calls': {
                'gemini': 0,
                'telegram': 0
            },
            'response_times': [],
            'last_cleanup': None,
            'pending_count': 0
        }
        self.stats_file = 'bot_stats.json'
        self.load_stats()
    
    def load_stats(self):
        """Загрузка статистики из файла"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    # Обновляем только сохраняемые поля
                    for key in ['processed_plugins', 'approved_plugins', 'rejected_plugins']:
                        if key in saved_stats:
                            self.stats[key] = saved_stats[key]
        except Exception as e:
            logger.error(f"Ошибка загрузки статистики: {e}")
    
    def save_stats(self):
        """Сохранение статистики в файл"""
        try:
            # Сохраняем только важные поля
            save_data = {
                'processed_plugins': self.stats['processed_plugins'],
                'approved_plugins': self.stats['approved_plugins'],
                'rejected_plugins': self.stats['rejected_plugins'],
                'last_save': datetime.now().isoformat()
            }
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Ошибка сохранения статистики: {e}")
    
    def record_plugin_processed(self):
        """Записать обработку плагина"""
        self.stats['processed_plugins'] += 1
        self.save_stats()
    
    def record_plugin_approved(self):
        """Записать одобрение плагина"""
        self.stats['approved_plugins'] += 1
        self.save_stats()
    
    def record_plugin_rejected(self):
        """Записать отклонение плагина"""
        self.stats['rejected_plugins'] += 1
        self.save_stats()
    
    def record_error(self):
        """Записать ошибку"""
        self.stats['errors'] += 1
    
    def record_api_call(self, api_type: str):
        """Записать вызов API"""
        if api_type in self.stats['api_calls']:
            self.stats['api_calls'][api_type] += 1
    
    def record_response_time(self, response_time: float):
        """Записать время ответа"""
        self.stats['response_times'].append(response_time)
        # Оставляем только последние 100 измерений
        if len(self.stats['response_times']) > 100:
            self.stats['response_times'] = self.stats['response_times'][-100:]
    
    def record_cleanup(self):
        """Записать выполнение очистки"""
        self.stats['last_cleanup'] = datetime.now()
    
    def update_pending_count(self, count: int):
        """Обновить количество ожидающих плагинов"""
        self.stats['pending_count'] = count
    
    def get_uptime(self) -> str:
        """Получить время работы"""
        uptime = datetime.now() - self.stats['start_time']
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}д {hours}ч {minutes}м"
        elif hours > 0:
            return f"{hours}ч {minutes}м"
        else:
            return f"{minutes}м"
    
    def get_average_response_time(self) -> float:
        """Получить среднее время ответа"""
        if not self.stats['response_times']:
            return 0.0
        return sum(self.stats['response_times']) / len(self.stats['response_times'])
    
    def get_stats_summary(self) -> str:
        """Получить сводку статистики"""
        uptime = self.get_uptime()
        avg_response = self.get_average_response_time()
        
        summary = f"📊 <b>Статистика бота</b>\n\n"
        summary += f"⏱ <b>Время работы:</b> {uptime}\n"
        summary += f"🔌 <b>Обработано плагинов:</b> {self.stats['processed_plugins']}\n"
        summary += f"✅ <b>Одобрено:</b> {self.stats['approved_plugins']}\n"
        summary += f"❌ <b>Отклонено:</b> {self.stats['rejected_plugins']}\n"
        summary += f"⏳ <b>Ожидают проверки:</b> {self.stats['pending_count']}\n"
        summary += f"🚨 <b>Ошибок:</b> {self.stats['errors']}\n\n"
        
        summary += f"🌐 <b>API вызовы:</b>\n"
        summary += f"• Gemini: {self.stats['api_calls']['gemini']}\n"
        summary += f"• Telegram: {self.stats['api_calls']['telegram']}\n\n"
        
        summary += f"⚡ <b>Среднее время ответа:</b> {avg_response:.2f}с\n"
        
        if self.stats['last_cleanup']:
            cleanup_time = self.stats['last_cleanup'].strftime('%d.%m.%Y %H:%M')
            summary += f"🧹 <b>Последняя очистка:</b> {cleanup_time}\n"
        
        return summary

class PerformanceMonitor:
    """Монитор производительности"""
    
    def __init__(self):
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            elapsed = time.time() - self.start_time
            logger.info(f"Операция выполнена за {elapsed:.2f}с")

class HealthChecker:
    """Проверка здоровья системы"""
    
    def __init__(self, bot, forum_manager):
        self.bot = bot
        self.forum_manager = forum_manager
        self.last_check = None
        self.check_interval = 300  # 5 минут
    
    async def run_health_checks(self) -> Dict[str, bool]:
        """Запуск проверок здоровья"""
        results = {}
        
        try:
            # Проверка подключения к Telegram
            results['telegram'] = await self._check_telegram()
            
            # Проверка настроек форума
            results['forum'] = await self._check_forum()
            
            # Проверка прав бота
            results['permissions'] = await self._check_permissions()
            
            self.last_check = datetime.now()
            
        except Exception as e:
            logger.error(f"Ошибка проверки здоровья: {e}")
            results['error'] = str(e)
        
        return results
    
    async def _check_telegram(self) -> bool:
        """Проверка подключения к Telegram"""
        try:
            await self.bot.get_me()
            return True
        except Exception as e:
            logger.error(f"Ошибка подключения к Telegram: {e}")
            return False
    
    async def _check_forum(self) -> bool:
        """Проверка настроек форума"""
        try:
            topic_info = await self.forum_manager.get_topic_info()
            return topic_info is not None and topic_info.get('is_forum', False)
        except Exception as e:
            logger.error(f"Ошибка проверки форума: {e}")
            return False
    
    async def _check_permissions(self) -> bool:
        """Проверка прав бота"""
        try:
            permissions = await self.forum_manager.check_bot_permissions()
            required_perms = ['can_send_documents', 'can_send_messages']
            return all(permissions.get(perm, False) for perm in required_perms)
        except Exception as e:
            logger.error(f"Ошибка проверки прав: {e}")
            return False
    
    def should_run_check(self) -> bool:
        """Нужно ли запускать проверку"""
        if not self.last_check:
            return True
        
        return (datetime.now() - self.last_check).seconds >= self.check_interval

class AlertManager:
    """Менеджер уведомлений"""
    
    def __init__(self, bot, admin_ids):
        self.bot = bot
        self.admin_ids = admin_ids
        self.alert_cooldown = {}
        self.cooldown_period = 3600  # 1 час
    
    async def send_alert(self, alert_type: str, message: str):
        """Отправка уведомления администраторам"""
        # Проверяем cooldown
        if self._is_on_cooldown(alert_type):
            return
        
        alert_message = f"🚨 <b>Уведомление системы</b>\n\n"
        alert_message += f"<b>Тип:</b> {alert_type}\n"
        alert_message += f"<b>Время:</b> {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}\n\n"
        alert_message += message
        
        for admin_id in self.admin_ids:
            try:
                await self.bot.send_message(admin_id, alert_message)
            except Exception as e:
                logger.error(f"Не удалось отправить уведомление админу {admin_id}: {e}")
        
        # Устанавливаем cooldown
        self.alert_cooldown[alert_type] = datetime.now()
    
    def _is_on_cooldown(self, alert_type: str) -> bool:
        """Проверка cooldown для типа уведомления"""
        if alert_type not in self.alert_cooldown:
            return False
        
        last_alert = self.alert_cooldown[alert_type]
        return (datetime.now() - last_alert).seconds < self.cooldown_period

# Глобальные экземпляры для использования в основном боте
monitoring = BotMonitoring()
