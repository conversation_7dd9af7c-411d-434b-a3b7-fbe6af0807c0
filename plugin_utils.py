import re
import zipfile
import tempfile
import os
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class PluginAnalyzer:
    """Утилиты для анализа плагинов"""
    
    DANGEROUS_PATTERNS = [
        # Выполнение кода
        r'eval\s*\(',
        r'exec\s*\(',
        r'os\.system\s*\(',
        r'subprocess\.',
        r'__import__\s*\(',
        
        # Сетевые запросы к подозрительным доменам
        r'requests\.(get|post)\s*\(\s*["\'](?!https://)',
        r'urllib\.request\.',
        r'socket\.',
        
        # Файловые операции
        r'open\s*\([^)]*["\']w["\']',
        r'os\.remove\s*\(',
        r'os\.rmdir\s*\(',
        r'shutil\.rmtree\s*\(',
        
        # Доступ к системным ресурсам
        r'os\.environ',
        r'getpass\.',
        r'keyring\.',
        
        # Обфускация
        r'base64\.b64decode\s*\(',
        r'codecs\.decode\s*\(',
        r'bytes\.fromhex\s*\(',
    ]
    
    SUSPICIOUS_PATTERNS = [
        # Сетевые запросы без HTTPS
        r'http://[^"\']+',
        r'requests\.(get|post).*verify\s*=\s*False',
        
        # Доступ к личным данным
        r'contact',
        r'phone',
        r'location',
        
        # Файловые операции в корневых папках
        r'open\s*\(\s*["\'][/\\]',
        r'/home/',
        r'C:\\\\',
    ]
    
    SAFE_PATTERNS = [
        # Стандартные API ExteraGram
        r'BasePlugin',
        r'send_message',
        r'get_setting',
        r'set_setting',
        r'BulletinHelper',
        r'AlertDialogBuilder',
        
        # Безопасные библиотеки
        r'import json',
        r'import datetime',
        r'import re',
        r'import math',
        
        # Безопасные API
        r'https://wttr\.in',
        r'https://api\.github\.com',
        r'https://.*\.googleapis\.com',
    ]
    
    @classmethod
    def analyze_code_patterns(cls, code: str) -> Dict[str, List[str]]:
        """Анализ кода на наличие подозрительных паттернов"""
        results = {
            'dangerous': [],
            'suspicious': [],
            'safe': []
        }
        
        # Проверяем опасные паттерны
        for pattern in cls.DANGEROUS_PATTERNS:
            matches = re.findall(pattern, code, re.IGNORECASE | re.MULTILINE)
            if matches:
                results['dangerous'].extend(matches)
        
        # Проверяем подозрительные паттерны
        for pattern in cls.SUSPICIOUS_PATTERNS:
            matches = re.findall(pattern, code, re.IGNORECASE | re.MULTILINE)
            if matches:
                results['suspicious'].extend(matches)
        
        # Проверяем безопасные паттерны
        for pattern in cls.SAFE_PATTERNS:
            matches = re.findall(pattern, code, re.IGNORECASE | re.MULTILINE)
            if matches:
                results['safe'].extend(matches)
        
        return results
    
    @classmethod
    def extract_imports(cls, code: str) -> List[str]:
        """Извлечение всех импортов из кода"""
        import_patterns = [
            r'import\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)',
            r'from\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s+import',
        ]
        
        imports = []
        for pattern in import_patterns:
            matches = re.findall(pattern, code, re.MULTILINE)
            imports.extend(matches)
        
        return list(set(imports))
    
    @classmethod
    def extract_functions(cls, code: str) -> List[str]:
        """Извлечение всех функций из кода"""
        function_pattern = r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
        functions = re.findall(function_pattern, code, re.MULTILINE)
        return functions
    
    @classmethod
    def extract_classes(cls, code: str) -> List[str]:
        """Извлечение всех классов из кода"""
        class_pattern = r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[\(:]'
        classes = re.findall(class_pattern, code, re.MULTILINE)
        return classes
    
    @classmethod
    def calculate_complexity_score(cls, code: str) -> int:
        """Расчет сложности кода"""
        score = 0
        
        # Базовая сложность по количеству строк
        lines = len([line for line in code.split('\n') if line.strip()])
        score += min(lines // 10, 50)  # Максимум 50 баллов за размер
        
        # Сложность по количеству функций
        functions = cls.extract_functions(code)
        score += len(functions) * 2
        
        # Сложность по количеству классов
        classes = cls.extract_classes(code)
        score += len(classes) * 5
        
        # Сложность по количеству импортов
        imports = cls.extract_imports(code)
        score += len(imports)
        
        # Сложность по вложенности
        max_indent = 0
        for line in code.split('\n'):
            if line.strip():
                indent = len(line) - len(line.lstrip())
                max_indent = max(max_indent, indent // 4)
        score += max_indent * 3
        
        return min(score, 100)  # Максимум 100 баллов

class PluginExtractor:
    """Утилиты для извлечения содержимого плагинов"""
    
    SUPPORTED_EXTENSIONS = ['.py', '.js', '.java', '.kt', '.ts']
    
    @classmethod
    def extract_from_file(cls, file_path: str) -> Dict[str, str]:
        """Извлечение содержимого из файла плагина"""
        try:
            if file_path.endswith('.zip'):
                return cls._extract_from_zip(file_path)
            else:
                return cls._extract_from_single_file(file_path)
        except Exception as e:
            logger.error(f"Ошибка извлечения содержимого из {file_path}: {e}")
            return {}
    
    @classmethod
    def _extract_from_zip(cls, zip_path: str) -> Dict[str, str]:
        """Извлечение содержимого из ZIP архива"""
        contents = {}
        
        with zipfile.ZipFile(zip_path, 'r') as zip_file:
            for file_name in zip_file.namelist():
                if any(file_name.endswith(ext) for ext in cls.SUPPORTED_EXTENSIONS):
                    try:
                        with zip_file.open(file_name) as f:
                            content = f.read().decode('utf-8', errors='ignore')
                            contents[file_name] = content
                    except Exception as e:
                        logger.warning(f"Не удалось прочитать файл {file_name}: {e}")
        
        return contents
    
    @classmethod
    def _extract_from_single_file(cls, file_path: str) -> Dict[str, str]:
        """Извлечение содержимого из одиночного файла"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                return {os.path.basename(file_path): content}
        except Exception as e:
            logger.error(f"Ошибка чтения файла {file_path}: {e}")
            return {}

class PluginValidator:
    """Валидатор плагинов"""
    
    REQUIRED_FIELDS_RU = [
        'название:', 'автор:', 'описание:', 'использование:'
    ]
    
    REQUIRED_FIELDS_EN = [
        'name:', 'author:', 'description:', 'usage:'
    ]
    
    @classmethod
    def validate_description(cls, description: str) -> Tuple[bool, List[str]]:
        """Валидация описания плагина"""
        errors = []
        description_lower = description.lower()
        
        # Проверяем наличие русской секции
        has_ru_section = '🇷🇺 [ru]:' in description_lower
        if not has_ru_section:
            errors.append("Отсутствует русская секция описания")
        
        # Проверяем наличие английской секции
        has_en_section = '🇺🇸 [en]:' in description_lower
        if not has_en_section:
            errors.append("Отсутствует английская секция описания")
        
        # Проверяем обязательные поля для русской секции
        if has_ru_section:
            missing_ru = [field for field in cls.REQUIRED_FIELDS_RU 
                         if field not in description_lower]
            if missing_ru:
                errors.append(f"Отсутствуют обязательные поля в русской секции: {', '.join(missing_ru)}")
        
        # Проверяем обязательные поля для английской секции
        if has_en_section:
            missing_en = [field for field in cls.REQUIRED_FIELDS_EN 
                         if field not in description_lower]
            if missing_en:
                errors.append(f"Отсутствуют обязательные поля в английской секции: {', '.join(missing_en)}")
        
        # Проверяем минимальную длину описания
        if len(description.strip()) < 100:
            errors.append("Описание слишком короткое (минимум 100 символов)")
        
        return len(errors) == 0, errors
    
    @classmethod
    def validate_file_structure(cls, contents: Dict[str, str]) -> Tuple[bool, List[str]]:
        """Валидация структуры файлов плагина"""
        errors = []
        
        if not contents:
            errors.append("Не удалось извлечь содержимое плагина")
            return False, errors
        
        # Проверяем наличие основного файла
        main_files = [name for name in contents.keys() 
                     if any(name.endswith(ext) for ext in PluginExtractor.SUPPORTED_EXTENSIONS)]
        
        if not main_files:
            errors.append("Не найден основной файл плагина")
        
        # Проверяем, что файлы не пустые
        empty_files = [name for name, content in contents.items() 
                      if not content.strip()]
        
        if empty_files:
            errors.append(f"Пустые файлы: {', '.join(empty_files)}")
        
        return len(errors) == 0, errors
