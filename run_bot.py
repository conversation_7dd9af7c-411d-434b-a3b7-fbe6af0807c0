#!/usr/bin/env python3
"""
Скрипт запуска Plugin Security Bot с проверками
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Добавляем текущую директорию в путь
sys.path.insert(0, str(Path(__file__).parent))

from dotenv import load_dotenv

def setup_logging():
    """Настройка логирования"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('bot.log', encoding='utf-8')
        ]
    )

def check_environment():
    """Проверка переменных окружения"""
    load_dotenv()
    
    required_vars = [
        'BOT_TOKEN',
        'GEMINI_API_KEY', 
        'FORUM_CHAT_ID',
        'PLUGINS_TOPIC_ID',
        'ADMIN_IDS'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Отсутствуют обязательные переменные окружения:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nСкопируйте .env.example в .env и заполните необходимые данные")
        return False
    
    print("✅ Все переменные окружения настроены")
    return True

def check_dependencies():
    """Проверка зависимостей"""
    try:
        import aiogram
        import google.generativeai
        import aiohttp
        print("✅ Все зависимости установлены")
        return True
    except ImportError as e:
        print(f"❌ Отсутствует зависимость: {e}")
        print("Установите зависимости: pip install -r requirements.txt")
        return False

async def test_bot_connection():
    """Тест подключения к Telegram Bot API"""
    try:
        from aiogram import Bot
        from aiogram.client.default import DefaultBotProperties
        from aiogram.enums import ParseMode
        
        bot_token = os.getenv('BOT_TOKEN')
        bot = Bot(token=bot_token, default=DefaultBotProperties(parse_mode=ParseMode.HTML))
        
        me = await bot.get_me()
        print(f"✅ Подключение к боту успешно: @{me.username}")
        await bot.session.close()
        return True
        
    except Exception as e:
        print(f"❌ Ошибка подключения к боту: {e}")
        return False

async def test_gemini_connection():
    """Тест подключения к Gemini API"""
    try:
        import google.generativeai as genai
        
        api_key = os.getenv('GEMINI_API_KEY')
        genai.configure(api_key=api_key)
        
        model = genai.GenerativeModel('gemini-1.5-flash')
        response = await asyncio.to_thread(
            model.generate_content, 
            "Тест подключения. Ответь одним словом: OK"
        )
        
        if "OK" in response.text.upper():
            print("✅ Подключение к Gemini API успешно")
            return True
        else:
            print("⚠️ Gemini API отвечает, но ответ неожиданный")
            return True
            
    except Exception as e:
        print(f"❌ Ошибка подключения к Gemini API: {e}")
        return False

async def run_checks():
    """Запуск всех проверок"""
    print("🔍 Проверка настроек Plugin Security Bot...\n")
    
    # Проверка переменных окружения
    if not check_environment():
        return False
    
    # Проверка зависимостей
    if not check_dependencies():
        return False
    
    # Тест подключения к боту
    if not await test_bot_connection():
        return False
    
    # Тест подключения к Gemini
    if not await test_gemini_connection():
        return False
    
    print("\n✅ Все проверки пройдены успешно!")
    return True

async def main():
    """Главная функция"""
    setup_logging()
    
    # Запускаем проверки
    if not await run_checks():
        print("\n❌ Проверки не пройдены. Исправьте ошибки и попробуйте снова.")
        sys.exit(1)
    
    print("\n🚀 Запуск Plugin Security Bot...")
    
    try:
        # Импортируем и запускаем основной бот
        from plugin_security_bot import main as bot_main
        await bot_main()
        
    except KeyboardInterrupt:
        print("\n⏹️ Бот остановлен пользователем")
    except Exception as e:
        logging.error(f"Критическая ошибка: {e}")
        print(f"\n💥 Критическая ошибка: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 До свидания!")
    except Exception as e:
        print(f"\n💥 Неожиданная ошибка: {e}")
        sys.exit(1)
