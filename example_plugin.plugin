# Пример безопасного плагина для ExteraGram
# Этот файл можно использовать для тестирования бота

from base_plugin import BasePlugin
from client_utils import send_message
from ui.bulletin import BulletinHelper
import json
import datetime

class WeatherPlugin(BasePlugin):
    """Плагин для получения информации о погоде"""
    
    def __init__(self):
        super().__init__()
        self.name = "Weather Plugin"
        self.version = "1.0.0"
        self.description = "Получение информации о погоде"
    
    def on_plugin_load(self):
        """Вызывается при загрузке плагина"""
        self.log("Weather Plugin загружен")
        self.add_command_handler("weather", self.handle_weather_command)
    
    def on_plugin_unload(self):
        """Вызывается при выгрузке плагина"""
        self.log("Weather Plugin выгружен")
    
    async def handle_weather_command(self, message, args):
        """Обработчик команды погоды"""
        try:
            if not args:
                city = self.get_setting("default_city", "Moscow")
            else:
                city = " ".join(args)
            
            # Безопасный запрос к API погоды
            weather_data = await self.get_weather_data(city)
            
            if weather_data:
                response = self.format_weather_response(weather_data)
                await send_message({
                    'peer': message.peer_id,
                    'message': response
                })
            else:
                BulletinHelper.show_error("Не удалось получить данные о погоде")
                
        except Exception as e:
            self.log(f"Ошибка в команде погоды: {e}")
            BulletinHelper.show_error("Произошла ошибка при получении погоды")
    
    async def get_weather_data(self, city):
        """Получение данных о погоде через безопасный API"""
        try:
            import requests
            
            # Используем безопасный HTTPS API
            url = f"https://wttr.in/{city}?format=j1"
            
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            self.log(f"Ошибка получения данных о погоде: {e}")
            return None
    
    def format_weather_response(self, data):
        """Форматирование ответа о погоде"""
        try:
            current = data['current_condition'][0]
            location = data['nearest_area'][0]
            
            temp = current['temp_C']
            desc = current['weatherDesc'][0]['value']
            humidity = current['humidity']
            wind = current['windspeedKmph']
            
            city_name = location['areaName'][0]['value']
            country = location['country'][0]['value']
            
            response = f"🌤 Погода в {city_name}, {country}:\n\n"
            response += f"🌡 Температура: {temp}°C\n"
            response += f"☁️ Описание: {desc}\n"
            response += f"💧 Влажность: {humidity}%\n"
            response += f"💨 Ветер: {wind} км/ч\n"
            response += f"🕐 Обновлено: {datetime.datetime.now().strftime('%H:%M')}"
            
            return response
            
        except Exception as e:
            self.log(f"Ошибка форматирования ответа: {e}")
            return "Ошибка обработки данных о погоде"
    
    def create_settings(self):
        """Создание настроек плагина"""
        from ui.settings import Header, Input, Text
        
        settings = []
        
        settings.append(Header("Настройки погоды"))
        settings.append(Text("Настройте город по умолчанию для получения погоды"))
        
        settings.append(Input(
            title="Город по умолчанию",
            key="default_city",
            default_value="Moscow",
            hint="Введите название города"
        ))
        
        return settings

# Экспорт плагина
plugin_class = WeatherPlugin
