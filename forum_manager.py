import asyncio
import logging
from typing import List, Optional
from datetime import datetime, timedelta

from aiogram import Bo<PERSON>
from aiogram.types import Message
from aiogram.exceptions import TelegramAPIError

logger = logging.getLogger(__name__)

class ForumManager:
    """Менеджер для работы с форумом и топиками"""
    
    def __init__(self, bot: <PERSON><PERSON>, forum_chat_id: str, plugins_topic_id: int):
        self.bot = bot
        self.forum_chat_id = forum_chat_id
        self.plugins_topic_id = plugins_topic_id
        self.cleanup_interval = 3600  # Очистка каждый час
        self.max_message_age = 24 * 60 * 60  # 24 часа
        
    async def start_cleanup_task(self):
        """Запуск задачи автоматической очистки"""
        while True:
            try:
                await self.cleanup_old_plugins()
                await asyncio.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Ошибка в задаче очистки: {e}")
                await asyncio.sleep(300)  # Повторить через 5 минут при ошибке
    
    async def cleanup_old_plugins(self):
        """Удаление старых плагинов из топика"""
        try:
            # Получаем сообщения из топика
            messages = await self.get_topic_messages(limit=100)
            
            current_time = datetime.now()
            deleted_count = 0
            
            for message in messages:
                # Проверяем возраст сообщения
                message_age = (current_time - message.date).total_seconds()
                
                if message_age > self.max_message_age:
                    # Проверяем, является ли сообщение плагином
                    if self.is_plugin_message(message):
                        try:
                            await self.bot.delete_message(
                                chat_id=self.forum_chat_id,
                                message_id=message.message_id
                            )
                            deleted_count += 1
                            logger.info(f"Удален старый плагин: {message.message_id}")
                            
                            # Небольшая задержка между удалениями
                            await asyncio.sleep(0.5)
                            
                        except TelegramAPIError as e:
                            logger.warning(f"Не удалось удалить сообщение {message.message_id}: {e}")
            
            if deleted_count > 0:
                logger.info(f"Очистка завершена. Удалено сообщений: {deleted_count}")
                
        except Exception as e:
            logger.error(f"Ошибка при очистке топика: {e}")
    
    async def get_topic_messages(self, limit: int = 100) -> List[Message]:
        """Получение сообщений из топика"""
        try:
            # Получаем историю сообщений из топика
            # Примечание: В реальной реализации может потребоваться использование
            # других методов API для получения сообщений из конкретного топика
            
            # Это упрощенная версия - в реальности нужно использовать
            # метод get_chat_history или аналогичный
            messages = []
            
            # Здесь должна быть логика получения сообщений из топика
            # Пока возвращаем пустой список
            return messages
            
        except Exception as e:
            logger.error(f"Ошибка получения сообщений топика: {e}")
            return []
    
    def is_plugin_message(self, message: Message) -> bool:
        """Проверка, является ли сообщение плагином"""
        if not message.document:
            return False
        
        # Проверяем расширение файла
        if not message.document.file_name:
            return False
        
        if not message.document.file_name.endswith('.plugin'):
            return False
        
        # Проверяем наличие описания плагина в подписи
        if not message.caption:
            return False
        
        # Проверяем наличие ключевых слов в описании
        caption_lower = message.caption.lower()
        plugin_keywords = ['название:', 'name:', 'автор:', 'author:', 'описание:', 'description:']
        
        return any(keyword in caption_lower for keyword in plugin_keywords)
    
    async def delete_message_by_id(self, message_id: int) -> bool:
        """Удаление сообщения по ID"""
        try:
            await self.bot.delete_message(
                chat_id=self.forum_chat_id,
                message_id=message_id
            )
            logger.info(f"Сообщение {message_id} успешно удалено")
            return True
        except TelegramAPIError as e:
            logger.error(f"Не удалось удалить сообщение {message_id}: {e}")
            return False
    
    async def get_topic_info(self) -> Optional[dict]:
        """Получение информации о топике"""
        try:
            # Получаем информацию о чате
            chat = await self.bot.get_chat(self.forum_chat_id)
            
            return {
                "chat_id": chat.id,
                "title": chat.title,
                "type": chat.type,
                "is_forum": getattr(chat, 'is_forum', False)
            }
        except Exception as e:
            logger.error(f"Ошибка получения информации о топике: {e}")
            return None
    
    async def send_plugin_to_topic(self, document_file_id: str, caption: str) -> Optional[Message]:
        """Отправка плагина в топик"""
        try:
            message = await self.bot.send_document(
                chat_id=self.forum_chat_id,
                message_thread_id=self.plugins_topic_id,
                document=document_file_id,
                caption=caption,
                parse_mode="HTML"
            )
            logger.info(f"Плагин отправлен в топик: {message.message_id}")
            return message
        except TelegramAPIError as e:
            logger.error(f"Ошибка отправки плагина в топик: {e}")
            return None
    
    async def check_bot_permissions(self) -> dict:
        """Проверка прав бота в форуме"""
        try:
            chat_member = await self.bot.get_chat_member(
                chat_id=self.forum_chat_id,
                user_id=(await self.bot.get_me()).id
            )
            
            permissions = {
                "can_delete_messages": getattr(chat_member, 'can_delete_messages', False),
                "can_send_messages": getattr(chat_member, 'can_send_messages', False),
                "can_send_documents": getattr(chat_member, 'can_send_documents', False),
                "can_manage_topics": getattr(chat_member, 'can_manage_topics', False),
                "status": chat_member.status
            }
            
            return permissions
        except Exception as e:
            logger.error(f"Ошибка проверки прав бота: {e}")
            return {}
    
    async def validate_setup(self) -> bool:
        """Проверка корректности настройки форума и топика"""
        try:
            # Проверяем информацию о топике
            topic_info = await self.get_topic_info()
            if not topic_info:
                logger.error("Не удалось получить информацию о форуме")
                return False
            
            if not topic_info.get('is_forum', False):
                logger.error("Указанный чат не является форумом")
                return False
            
            # Проверяем права бота
            permissions = await self.check_bot_permissions()
            if not permissions.get('can_send_documents', False):
                logger.error("Бот не может отправлять документы в форум")
                return False
            
            if not permissions.get('can_delete_messages', False):
                logger.warning("Бот не может удалять сообщения (функция очистки недоступна)")
            
            logger.info("Настройка форума проверена успешно")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка валидации настройки: {e}")
            return False
