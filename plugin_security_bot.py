import asyncio
import logging
import os
import tempfile
import zipfile
from typing import Optional
from datetime import datetime

import aiohttp
import google.generativeai as genai
from aiogram import Bo<PERSON>, Dispatcher, F
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode
from aiogram.filters import Command
from aiogram.types import Message, Document, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from aiogram.utils.keyboard import InlineKeyboardBuilder
from dotenv import load_dotenv

from forum_manager import ForumManager
from plugin_utils import PluginAnalyzer, PluginExtractor, PluginValidator
from monitoring import monitoring, PerformanceMonitor

# Загружаем переменные окружения
load_dotenv()

# Настройки
BOT_TOKEN = os.getenv("BOT_TOKEN")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
FORUM_CHAT_ID = os.getenv("FORUM_CHAT_ID")  # ID форума
PLUGINS_TOPIC_ID = int(os.getenv("PLUGINS_TOPIC_ID", "0"))  # ID топика "Plugins"
ADMIN_IDS = list(map(int, os.getenv("ADMIN_IDS", "").split(","))) if os.getenv("ADMIN_IDS") else []

# Проверяем обязательные переменные
if not BOT_TOKEN:
    raise ValueError("BOT_TOKEN не установлен")
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY не установлен")
if not FORUM_CHAT_ID:
    raise ValueError("FORUM_CHAT_ID не установлен")
if not PLUGINS_TOPIC_ID:
    raise ValueError("PLUGINS_TOPIC_ID не установлен")
if not ADMIN_IDS:
    raise ValueError("ADMIN_IDS не установлен")

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Настройка Gemini AI
genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel('gemini-2.5-flash')

# Инициализация бота
bot = Bot(token=BOT_TOKEN, default=DefaultBotProperties(parse_mode=ParseMode.HTML))
dp = Dispatcher()

# Инициализация менеджера форума
forum_manager = ForumManager(bot, FORUM_CHAT_ID, PLUGINS_TOPIC_ID)

# Хранилище для ожидающих подтверждения плагинов
pending_plugins = {}

# Промпт для проверки плагинов
SECURITY_PROMPT = """
Ты — ведущий аналитик по кибербезопасности, специализирующийся именно на аудите плагинов для фреймворка ExteraGram. Твоя задача — провести строгий технический аудит кода плагина '{plugin_name}' (v{plugin_version}), опираясь на знание его API, и вынести точный вердикт по 5-уровневой шкале риска. Обрати внимание: код может быть предварительно обработан для анонимизации (переименованы переменные и строки), анализируй только логику.

--- Контекст ExteraGram API (Это считается БЕЗОПАСНЫМ) ---
Любое использование следующих официальных API ExteraGram является стандартной и безопасной практикой, а не угрозой:

• БАЗОВЫЙ ПЛАГИН API (base_plugin):
  - BasePlugin класс и его методы (on_plugin_load, on_plugin_unload, create_settings)
  - HookResult, HookStrategy для перехвата и модификации вызовов
  - Хуки: add_hook, add_on_send_message_hook, pre_request_hook, post_request_hook
  - Настройки: get_setting, set_setting для сохранения конфигурации
  - Логирование: self.log() для отладочных сообщений

• КЛИЕНТСКИЕ УТИЛИТЫ (client_utils):
  - Отправка сообщений: send_message с параметрами peer, message, entities, replyToMsg
  - API запросы: send_request с TLRPC объектами и RequestCallback
  - Фоновые задачи: run_on_queue с очередями PLUGINS_QUEUE, GLOBAL_QUEUE, EXTERNAL_NETWORK_QUEUE
  - Контроллеры: get_messages_controller, get_user_config, get_file_loader, get_last_fragment
  - Получение данных: get_account_instance, get_connections_manager

• ANDROID УТИЛИТЫ (android_utils):
  - UI поток: run_on_ui_thread для обновления интерфейса
  - Буфер обмена: AndroidUtilities.addToClipboard
  - Логирование: log() функция для отладки

• ПОЛЬЗОВАТЕЛЬСКИЙ ИНТЕРФЕЙС:
  - Настройки (ui.settings): Header, Input, Switch, Selector, Text, Divider
  - Диалоги (ui.alert): AlertDialogBuilder с типами ALERT_TYPE_MESSAGE, ALERT_TYPE_SPINNER
  - Уведомления (ui.bulletin): BulletinHelper.show_info, show_error, show_success

• TELEGRAM API (TLRPC):
  - Создание запросов: TLRPC.TL_messages_*, TLRPC.TL_users_*, TLRPC.TL_channels_*
  - Объекты сообщений: MessageObject, TLRPC.Message
  - Пользователи и чаты: TLRPC.User, TLRPC.Chat, TLRPC.UserFull
  - Медиа: TLRPC.Document, TLRPC.Photo, FileLoader для загрузки файлов

• РАЗМЕТКА И ФОРМАТИРОВАНИЕ:
  - markdown_utils: parse_markdown для обработки разметки
  - Entities: TLRPC.MessageEntity для форматирования текста

• БЕЗОПАСНЫЕ СЕТЕВЫЕ ЗАПРОСЫ:
  - requests.get/post к известным API (wttr.in, check-host.net, GitHub, Google APIs)
  - Таймауты и обработка ошибок с try/except
  - User-Agent заголовки с идентификацией плагина

• РАБОТА С ФАЙЛАМИ:
  - Чтение/запись в подпапки плагина внутри кеша приложения
  - get_file_loader().getPathToAttach() для доступа к вложениям
  - os.path операции в безопасных директориях

• JAVA ИНТЕГРАЦИЯ:
  - java.util классы: ArrayList, Locale
  - dynamic_proxy для создания Java интерфейсов
  - Импорты из org.telegram.* пакетов
--- Конец контекста ---

Принципы анализа и шкала рисков (приоритет от высшего к низшему):

1. ❌ Опасно - Явный вредоносный код:
   • Отправка критичных данных на сторонние серверы: содержимое сообщений, пароли, токены сессий, ключи API
   • Исполнение произвольного кода: eval(), exec(), os.system(), subprocess с пользовательским вводом
   • Деструктивные файловые операции: удаление, шифрование файлов пользователя вне папки плагина
   • Обход безопасности: попытки получить root права, отключить защиту
   • Кража данных: скрытая отправка личной информации без согласия пользователя

2. 📛 Высокий риск - Серьезная угроза приватности:
   • Сбор личных данных: ID пользователя, номер телефона, имя, список чатов/контактов
   • Отправка метаданных на сторонние серверы без явной необходимости
   • Доступ к чувствительным API: контакты, SMS, геолокация, камера, микрофон
   • Сохранение личных данных в незащищенном виде
   • Передача данных по незащищенным каналам (HTTP вместо HTTPS)

3. ⚠️ Осторожно - Подозрительные действия:
   • Сетевые запросы к неизвестным доменам без HTTPS
   • Запись файлов в корень общих папок вместо подпапки плагина
   • Использование устаревших или небезопасных библиотек
   • Отсутствие проверки SSL сертификатов
   • Чрезмерные разрешения без обоснования
   • Обфускация кода без технической необходимости

4. ❔ Низкий риск - Незначительные недочеты:
   • Доступ к буферу обмена (это нормально для функций копирования)
   • Использование устаревших, но не уязвимых библиотек
   • Отсутствие обработки ошибок (может привести к сбоям, но не к утечкам)
   • Неоптимальная архитектура кода
   • Отсутствие валидации пользовательского ввода (если не критично)

5. ✅ Безопасно - Соответствует стандартам:
   • Использует только официальные API ExteraGram из контекста выше
   • Стандартные библиотеки Python для обработки данных
   • Правильная обработка ошибок и исключений
   • HTTPS запросы к известным и надежным API
   • Корректная работа с файлами в разрешенных директориях
   • Прозрачная логика без скрытых действий

ВАЖНЫЕ ПРИМЕРЫ БЕЗОПАСНЫХ ПАТТЕРНОВ:
• requests.get('https://wttr.in/Moscow?format=j1', timeout=10) - погодный API
• send_message({{'peer': peer_id, 'message': text}}) - отправка сообщения
• run_on_queue(lambda: background_task()) - фоновая задача
• BulletinHelper.show_info('Готово!') - уведомление пользователю
• self.get_setting('api_key', '') - чтение настроек
• AlertDialogBuilder(context, ALERT_TYPE_MESSAGE) - диалоговое окно

Формат ответа:
◈ Вердикт: [Эмодзи] [Безопасно / Низкий риск / Осторожно / Высокий риск / Опасно]

☶ Назначение: [ОДНО ПРЕДЛОЖЕНИЕ, описывающее функцию плагина]

❏ Анализ:
• [Краткий вывод. Если рисков нет, напиши: Анализ не выявил действий, угрожающих безопасности. Плагин использует стандартные API ExteraGram.]
• [Краткое описание КАЖДОГО технического риска, если он есть. Укажи его уровень и почему это риск.]

Код для анализа:
{plugin_code}
"""

async def analyze_plugin_with_gemini(plugin_name: str, plugin_version: str, plugin_code: str) -> dict:
    """Анализ плагина с помощью Gemini AI"""
    try:
        prompt = SECURITY_PROMPT.format(
            plugin_name=plugin_name,
            plugin_version=plugin_version,
            plugin_code=plugin_code
        )
        
        response = await asyncio.to_thread(model.generate_content, prompt)
        analysis_text = response.text
        
        # Определяем уровень риска из ответа
        risk_level = "UNKNOWN"
        if "Безопасно" in analysis_text:
            risk_level = "SAFE"
        elif "Низкий риск" in analysis_text:
            risk_level = "LOW"
        elif "Осторожно" in analysis_text:
            risk_level = "MEDIUM"
        elif "Высокий риск" in analysis_text:
            risk_level = "HIGH"
        elif "Опасно" in analysis_text:
            risk_level = "DANGEROUS"
        
        return {
            "risk_level": risk_level,
            "analysis": analysis_text,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Ошибка анализа плагина: {e}")
        return {
            "risk_level": "ERROR",
            "analysis": f"Ошибка анализа: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }

def extract_plugin_info(content: str) -> dict:
    """Извлечение информации о плагине из описания"""
    lines = content.split('\n')
    info = {
        "name_ru": "",
        "name_en": "",
        "author": "",
        "description_ru": "",
        "description_en": "",
        "usage_ru": "",
        "usage_en": "",
        "settings": "",
        "updated": ""
    }
    
    current_section = None
    for line in lines:
        line = line.strip()
        if "🇷🇺 [RU]:" in line:
            current_section = "ru"
        elif "🇺🇸 [EN]:" in line:
            current_section = "en"
        elif line.startswith("Название:") and current_section == "ru":
            info["name_ru"] = line.replace("Название:", "").strip()
        elif line.startswith("Name:") and current_section == "en":
            info["name_en"] = line.replace("Name:", "").strip()
        elif line.startswith("Автор:") and current_section == "ru":
            info["author"] = line.replace("Автор:", "").strip()
        elif line.startswith("Author:") and current_section == "en":
            if not info["author"]:
                info["author"] = line.replace("Author:", "").strip()
        elif line.startswith("Описание:") and current_section == "ru":
            info["description_ru"] = line.replace("Описание:", "").strip()
        elif line.startswith("Description:") and current_section == "en":
            info["description_en"] = line.replace("Description:", "").strip()
        elif line.startswith("Использование:") and current_section == "ru":
            info["usage_ru"] = line.replace("Использование:", "").strip()
        elif line.startswith("Usage:") and current_section == "en":
            info["usage_en"] = line.replace("Usage:", "").strip()
        elif line.startswith("Настройки:") and current_section == "ru":
            info["settings"] = line.replace("Настройки:", "").strip()
        elif line.startswith("Settings:") and current_section == "en":
            if not info["settings"]:
                info["settings"] = line.replace("Settings:", "").strip()
        elif line.startswith("Обновлено:") and current_section == "ru":
            info["updated"] = line.replace("Обновлено:", "").strip()
        elif line.startswith("Updated:") and current_section == "en":
            if not info["updated"]:
                info["updated"] = line.replace("Updated:", "").strip()
    
    return info

async def read_plugin_file(file_path: str) -> str:
    """Чтение содержимого файла плагина"""
    try:
        # Используем PluginExtractor для извлечения содержимого
        contents = PluginExtractor.extract_from_file(file_path)

        if not contents:
            return ""

        # Объединяем содержимое всех файлов
        combined_content = ""
        for file_name, content in contents.items():
            combined_content += f"\n# === {file_name} ===\n"
            combined_content += content
            combined_content += "\n"

        return combined_content

    except Exception as e:
        logger.error(f"Ошибка чтения файла плагина: {e}")
        return ""

@dp.message(Command("start"))
async def start_command(message: Message):
    """Обработчик команды /start"""
    await message.answer(
        "🔒 <b>Plugin Security Bot</b>\n\n"
        "Отправьте мне файл плагина (.plugin) с описанием в стандартном формате, "
        "и я проверю его безопасность с помощью ИИ анализа.\n\n"
        "Формат описания должен включать:\n"
        "• Название на русском и английском\n"
        "• Автор\n"
        "• Описание функций\n"
        "• Инструкции по использованию\n"
        "• Информация о настройках\n"
        "• Дата обновления"
    )

@dp.message(F.document)
async def handle_plugin_file(message: Message):
    """Обработчик файлов плагинов"""
    document: Document = message.document

    # Начинаем мониторинг производительности
    with PerformanceMonitor():
        # Записываем начало обработки
        monitoring.record_plugin_processed()

        # Проверяем расширение файла
        if not document.file_name.endswith('.plugin'):
            await message.answer("❌ Пожалуйста, отправьте файл с расширением .plugin")
            return

        # Проверяем наличие описания в сообщении
        if not message.caption:
            await message.answer(
                "❌ Пожалуйста, добавьте описание плагина в подпись к файлу в стандартном формате."
            )
            return

        # Валидируем описание плагина
        is_valid_description, description_errors = PluginValidator.validate_description(message.caption)
        if not is_valid_description:
            error_text = "❌ Ошибки в описании плагина:\n\n"
            for error in description_errors:
                error_text += f"• {error}\n"
            error_text += "\nПожалуйста, исправьте описание и отправьте плагин заново."
            await message.answer(error_text)
            return

        await message.answer("🔄 Анализирую плагин...")

        try:
            # Скачиваем файл
            file = await bot.get_file(document.file_id)
            monitoring.record_api_call('telegram')

            with tempfile.NamedTemporaryFile(delete=False, suffix='.plugin') as temp_file:
                await bot.download_file(file.file_path, temp_file.name)

                # Читаем содержимое плагина
                plugin_code = await read_plugin_file(temp_file.name)

                # Валидируем структуру файлов
                contents = PluginExtractor.extract_from_file(temp_file.name)
                is_valid_structure, structure_errors = PluginValidator.validate_file_structure(contents)

                # Удаляем временный файл
                os.unlink(temp_file.name)

            if not plugin_code:
                await message.answer("❌ Не удалось прочитать содержимое плагина")
                return

            if not is_valid_structure:
                error_text = "❌ Ошибки в структуре плагина:\n\n"
                for error in structure_errors:
                    error_text += f"• {error}\n"
                error_text += "\nПожалуйста, исправьте структуру файлов и отправьте плагин заново."
                await message.answer(error_text)
                return
        
        # Извлекаем информацию о плагине
        plugin_info = extract_plugin_info(message.caption)
        plugin_name = plugin_info.get("name_ru") or plugin_info.get("name_en") or "Unknown"
        
        # Анализируем плагин
        analysis = await analyze_plugin_with_gemini(
            plugin_name=plugin_name,
            plugin_version="1.0",  # Можно извлечь из описания
            plugin_code=plugin_code
        )
        
        # Формируем сообщение с результатами
        result_message = f"🔍 <b>Результат анализа плагина:</b>\n\n"
        result_message += f"📦 <b>Плагин:</b> {plugin_name}\n"
        result_message += f"👤 <b>Автор:</b> {plugin_info.get('author', 'Не указан')}\n\n"
        result_message += f"<b>Анализ безопасности:</b>\n{analysis['analysis']}\n\n"
        
        # Определяем действие на основе уровня риска
        if analysis['risk_level'] == 'DANGEROUS':
            # Опасный плагин - требует подтверждения админов
            plugin_id = f"{message.from_user.id}_{document.file_id}"
            pending_plugins[plugin_id] = {
                "message": message,
                "document": document,
                "plugin_info": plugin_info,
                "analysis": analysis,
                "plugin_code": plugin_code
            }
            
            # Создаем клавиатуру для админов
            keyboard = InlineKeyboardBuilder()
            keyboard.add(InlineKeyboardButton(
                text="✅ Одобрить", 
                callback_data=f"approve_{plugin_id}"
            ))
            keyboard.add(InlineKeyboardButton(
                text="❌ Отклонить", 
                callback_data=f"reject_{plugin_id}"
            ))
            
            result_message += "⚠️ <b>Плагин помечен как ОПАСНЫЙ и требует подтверждения администратора.</b>"
            
            # Отправляем админам
            for admin_id in ADMIN_IDS:
                try:
                    await bot.send_message(
                        admin_id,
                        result_message,
                        reply_markup=keyboard.as_markup()
                    )
                except Exception as e:
                    logger.error(f"Не удалось отправить сообщение админу {admin_id}: {e}")
            
            await message.answer(
                "⚠️ Ваш плагин помечен как потенциально опасный и отправлен на проверку администраторам."
            )
            
        else:
            # Безопасный плагин - публикуем сразу
            await publish_plugin_to_forum(document, plugin_info, analysis)
            await message.answer("✅ Плагин успешно опубликован в топике Plugins!")

        except Exception as e:
            monitoring.record_error()
            logger.error(f"Ошибка обработки плагина: {e}")
            await message.answer(f"❌ Произошла ошибка при анализе плагина: {str(e)}")

async def publish_plugin_to_forum(document: Document, plugin_info: dict, analysis: dict):
    """Публикация плагина в форум"""
    try:
        # Формируем сообщение для форума
        risk_emoji = {
            "SAFE": "✅",
            "LOW": "❔",
            "MEDIUM": "⚠️",
            "HIGH": "📛",
            "DANGEROUS": "❌",
            "ERROR": "🔧"
        }

        forum_message = f"🔌 <b>Новый плагин:</b> {plugin_info.get('name_ru', 'Unknown')}\n\n"
        forum_message += f"👤 <b>Автор:</b> {plugin_info.get('author', 'Не указан')}\n"
        forum_message += f"📝 <b>Описание:</b> {plugin_info.get('description_ru', 'Не указано')}\n"
        forum_message += f"🔧 <b>Использование:</b> {plugin_info.get('usage_ru', 'Не указано')}\n"
        forum_message += f"⚙️ <b>Настройки:</b> {plugin_info.get('settings', 'Не указаны')}\n"
        forum_message += f"📅 <b>Обновлено:</b> {plugin_info.get('updated', 'Не указано')}\n\n"

        risk_level = analysis.get('risk_level', 'UNKNOWN')
        emoji = risk_emoji.get(risk_level, "❓")
        forum_message += f"🔒 <b>Статус безопасности:</b> {emoji} {risk_level}\n"

        # Добавляем краткий анализ
        analysis_text = analysis.get('analysis', '')
        if len(analysis_text) > 500:
            forum_message += f"📊 <b>Анализ:</b> {analysis_text[:500]}...\n"
        else:
            forum_message += f"📊 <b>Анализ:</b> {analysis_text}\n"

        forum_message += f"\n🕐 <b>Проверено:</b> {datetime.now().strftime('%d.%m.%Y %H:%M')}"

        # Используем ForumManager для отправки
        result = await forum_manager.send_plugin_to_topic(document.file_id, forum_message)

        if not result:
            raise Exception("Не удалось отправить плагин в топик")

    except Exception as e:
        logger.error(f"Ошибка публикации в форум: {e}")
        raise

@dp.callback_query(F.data.startswith("approve_"))
async def approve_plugin(callback: CallbackQuery):
    """Одобрение плагина администратором"""
    if callback.from_user.id not in ADMIN_IDS:
        await callback.answer("❌ У вас нет прав для этого действия")
        return
    
    plugin_id = callback.data.replace("approve_", "")
    
    if plugin_id not in pending_plugins:
        await callback.answer("❌ Плагин не найден")
        return
    
    plugin_data = pending_plugins[plugin_id]
    
    try:
        # Публикуем плагин
        await publish_plugin_to_forum(
            plugin_data["document"],
            plugin_data["plugin_info"],
            plugin_data["analysis"]
        )

        # Записываем одобрение
        monitoring.record_plugin_approved()

        # Уведомляем пользователя
        await bot.send_message(
            plugin_data["message"].from_user.id,
            "✅ Ваш плагин был одобрен администратором и опубликован в топике Plugins!"
        )

        # Удаляем из ожидающих
        del pending_plugins[plugin_id]
        monitoring.update_pending_count(len(pending_plugins))
        
        await callback.message.edit_text(
            callback.message.text + f"\n\n✅ <b>ОДОБРЕНО</b> администратором @{callback.from_user.username}"
        )
        await callback.answer("✅ Плагин одобрен и опубликован")
        
    except Exception as e:
        logger.error(f"Ошибка одобрения плагина: {e}")
        await callback.answer(f"❌ Ошибка: {str(e)}")

@dp.callback_query(F.data.startswith("reject_"))
async def reject_plugin(callback: CallbackQuery):
    """Отклонение плагина администратором"""
    if callback.from_user.id not in ADMIN_IDS:
        await callback.answer("❌ У вас нет прав для этого действия")
        return

    plugin_id = callback.data.replace("reject_", "")

    if plugin_id not in pending_plugins:
        await callback.answer("❌ Плагин не найден")
        return

    plugin_data = pending_plugins[plugin_id]

    # Записываем отклонение
    monitoring.record_plugin_rejected()

    # Уведомляем пользователя
    await bot.send_message(
        plugin_data["message"].from_user.id,
        "❌ Ваш плагин был отклонен администратором по соображениям безопасности."
    )

    # Удаляем из ожидающих
    del pending_plugins[plugin_id]
    monitoring.update_pending_count(len(pending_plugins))

    await callback.message.edit_text(
        callback.message.text + f"\n\n❌ <b>ОТКЛОНЕНО</b> администратором @{callback.from_user.username}"
    )
    await callback.answer("❌ Плагин отклонен")

@dp.message(Command("admin"))
async def admin_panel(message: Message):
    """Панель администратора"""
    if message.from_user.id not in ADMIN_IDS:
        await message.answer("❌ У вас нет прав администратора")
        return

    pending_count = len(pending_plugins)

    admin_text = f"🔧 <b>Панель администратора</b>\n\n"
    admin_text += f"📊 <b>Статистика:</b>\n"
    admin_text += f"• Ожидают проверки: {pending_count} плагинов\n\n"

    admin_text += f"🛠 <b>Доступные команды:</b>\n"
    admin_text += f"• /status - Проверка статуса бота\n"
    admin_text += f"• /stats - Статистика работы\n"
    admin_text += f"• /cleanup - Очистка старых плагинов\n"
    admin_text += f"• /pending - Список ожидающих плагинов\n"

    await message.answer(admin_text)

@dp.message(Command("status"))
async def bot_status(message: Message):
    """Проверка статуса бота"""
    if message.from_user.id not in ADMIN_IDS:
        await message.answer("❌ У вас нет прав администратора")
        return

    await message.answer("🔄 Проверяю статус...")

    try:
        # Проверяем настройку форума
        is_valid = await forum_manager.validate_setup()

        # Получаем информацию о топике
        topic_info = await forum_manager.get_topic_info()

        # Проверяем права бота
        permissions = await forum_manager.check_bot_permissions()

        status_text = f"🤖 <b>Статус бота</b>\n\n"
        status_text += f"✅ Бот активен\n"
        status_text += f"{'✅' if is_valid else '❌'} Настройка форума: {'OK' if is_valid else 'Ошибка'}\n\n"

        if topic_info:
            status_text += f"📋 <b>Информация о форуме:</b>\n"
            status_text += f"• Название: {topic_info.get('title', 'Неизвестно')}\n"
            status_text += f"• ID: {topic_info.get('chat_id', 'Неизвестно')}\n"
            status_text += f"• Тип: {topic_info.get('type', 'Неизвестно')}\n"
            status_text += f"• Форум: {'Да' if topic_info.get('is_forum') else 'Нет'}\n\n"

        status_text += f"🔐 <b>Права бота:</b>\n"
        status_text += f"• Отправка документов: {'✅' if permissions.get('can_send_documents') else '❌'}\n"
        status_text += f"• Удаление сообщений: {'✅' if permissions.get('can_delete_messages') else '❌'}\n"
        status_text += f"• Управление топиками: {'✅' if permissions.get('can_manage_topics') else '❌'}\n"
        status_text += f"• Статус: {permissions.get('status', 'Неизвестно')}\n\n"

        status_text += f"📊 <b>Статистика:</b>\n"
        status_text += f"• Ожидают проверки: {len(pending_plugins)} плагинов\n"

        await message.answer(status_text)

    except Exception as e:
        await message.answer(f"❌ Ошибка проверки статуса: {str(e)}")

@dp.message(Command("cleanup"))
async def manual_cleanup(message: Message):
    """Ручная очистка старых плагинов"""
    if message.from_user.id not in ADMIN_IDS:
        await message.answer("❌ У вас нет прав администратора")
        return

    await message.answer("🔄 Запускаю очистку старых плагинов...")

    try:
        await forum_manager.cleanup_old_plugins()
        await message.answer("✅ Очистка завершена")
    except Exception as e:
        await message.answer(f"❌ Ошибка очистки: {str(e)}")

@dp.message(Command("pending"))
async def show_pending(message: Message):
    """Показать ожидающие плагины"""
    if message.from_user.id not in ADMIN_IDS:
        await message.answer("❌ У вас нет прав администратора")
        return

    if not pending_plugins:
        await message.answer("📭 Нет плагинов, ожидающих проверки")
        return

    pending_text = f"📋 <b>Ожидают проверки ({len(pending_plugins)}):</b>\n\n"

    for plugin_id, plugin_data in pending_plugins.items():
        plugin_info = plugin_data["plugin_info"]
        analysis = plugin_data["analysis"]

        pending_text += f"🔌 <b>{plugin_info.get('name_ru', 'Unknown')}</b>\n"
        pending_text += f"👤 Автор: {plugin_info.get('author', 'Не указан')}\n"
        pending_text += f"🔒 Риск: {analysis.get('risk_level', 'UNKNOWN')}\n"
        pending_text += f"ID: <code>{plugin_id}</code>\n\n"

    await message.answer(pending_text)

async def main():
    """Главная функция"""
    logger.info("Запуск Plugin Security Bot...")

    # Проверяем настройку форума при запуске
    try:
        is_valid = await forum_manager.validate_setup()
        if not is_valid:
            logger.warning("Настройка форума содержит ошибки, некоторые функции могут не работать")
        else:
            logger.info("Настройка форума проверена успешно")
    except Exception as e:
        logger.error(f"Ошибка проверки настройки форума: {e}")

    # Запускаем задачу автоматической очистки в фоне
    cleanup_task = asyncio.create_task(forum_manager.start_cleanup_task())

    try:
        # Запускаем polling
        await dp.start_polling(bot)
    finally:
        # Отменяем задачу очистки при завершении
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass

if __name__ == "__main__":
    asyncio.run(main())
