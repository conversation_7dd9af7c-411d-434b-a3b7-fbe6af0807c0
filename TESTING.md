# Тестирование Plugin Security Bot

## Подготовка к тестированию

### 1. Настройка окружения

1. Скопируйте `.env.example` в `.env`:
```bash
cp .env.example .env
```

2. Заполните все необходимые переменные в `.env`

3. Установите зависимости:
```bash
pip install -r requirements.txt
```

### 2. Создание тестового форума

1. Создайте новую группу в Telegram
2. Преобразуйте её в супергруппу
3. Включите топики (Topics) в настройках группы
4. Создайте топик с названием "Plugins"
5. Добавьте вашего бота как администратора с правами:
   - Удаление сообщений
   - Отправка сообщений
   - Управление топиками

### 3. Получение ID

Для получения ID чата и топика используйте @userinfobot или другие методы:

1. **ID чата**: Добавьте @userinfobot в группу и получите ID
2. **ID топика**: Отправьте сообщение в топик "Plugins" и посмотрите message_thread_id

## Запуск тестирования

### 1. Проверка настроек

```bash
python run_bot.py
```

Скрипт автоматически проверит:
- ✅ Переменные окружения
- ✅ Зависимости
- ✅ Подключение к Telegram Bot API
- ✅ Подключение к Gemini API

### 2. Тестовые сценарии

#### Сценарий 1: Безопасный плагин

1. Запустите бота: `python run_bot.py`
2. Отправьте боту файл `example_plugin.plugin`
3. В подпись к файлу скопируйте содержимое `example_description.txt`
4. Ожидаемый результат:
   - ✅ Бот проанализирует плагин
   - ✅ Определит его как безопасный
   - ✅ Автоматически опубликует в топике "Plugins"

#### Сценарий 2: Плагин с ошибками в описании

1. Отправьте файл плагина с неполным описанием (без обязательных полей)
2. Ожидаемый результат:
   - ❌ Бот отклонит плагин
   - ❌ Покажет список ошибок в описании

#### Сценарий 3: Опасный плагин

Создайте тестовый плагин с опасным кодом:

```python
import os
import subprocess

# Опасный код
os.system("rm -rf /")
subprocess.call(["malicious_command"])
```

Ожидаемый результат:
- ⚠️ Бот определит плагин как опасный
- ⚠️ Отправит на модерацию администраторам
- ⚠️ Администраторы получат уведомление с кнопками одобрения/отклонения

#### Сценарий 4: Команды администратора

Отправьте боту команды (от имени администратора):

1. `/admin` - панель администратора
2. `/status` - проверка статуса
3. `/pending` - список ожидающих плагинов
4. `/cleanup` - ручная очистка

## Проверка функций

### ✅ Анализ безопасности

- [ ] Определение безопасных плагинов
- [ ] Обнаружение опасных паттернов
- [ ] Правильная классификация рисков
- [ ] Генерация подробного анализа

### ✅ Валидация

- [ ] Проверка формата описания
- [ ] Валидация структуры файлов
- [ ] Проверка обязательных полей
- [ ] Обработка некорректных файлов

### ✅ Публикация

- [ ] Автоматическая публикация безопасных плагинов
- [ ] Правильное форматирование сообщений
- [ ] Отправка в корректный топик
- [ ] Добавление метаданных

### ✅ Модерация

- [ ] Отправка опасных плагинов на проверку
- [ ] Уведомления администраторов
- [ ] Кнопки одобрения/отклонения
- [ ] Уведомления пользователей о решении

### ✅ Автоматическая очистка

- [ ] Удаление старых плагинов
- [ ] Соблюдение временных интервалов
- [ ] Логирование операций очистки
- [ ] Ручной запуск очистки

### ✅ Администрирование

- [ ] Панель администратора
- [ ] Проверка статуса системы
- [ ] Просмотр ожидающих плагинов
- [ ] Управление настройками

## Отладка

### Логи

Бот создает файл `bot.log` с подробными логами. Проверьте его при возникновении проблем.

### Частые проблемы

1. **Бот не отвечает**
   - Проверьте токен бота
   - Убедитесь, что бот запущен

2. **Ошибки Gemini API**
   - Проверьте API ключ
   - Убедитесь в наличии квот

3. **Не публикует в форум**
   - Проверьте права бота в группе
   - Убедитесь в правильности ID чата и топика

4. **Не удаляет старые сообщения**
   - Проверьте права на удаление сообщений
   - Убедитесь, что функция очистки включена

### Мониторинг

Используйте команду `/status` для проверки:
- Статуса подключений
- Прав бота
- Настроек форума
- Статистики работы

## Производительность

### Рекомендуемые лимиты

- Максимум 10 плагинов в час на пользователя
- Размер файла плагина до 1 МБ
- Время анализа до 30 секунд

### Мониторинг ресурсов

- Использование памяти
- Время ответа API
- Количество обработанных плагинов
- Ошибки и исключения

## Безопасность

### Проверки безопасности

- [ ] Изоляция анализируемого кода
- [ ] Безопасное извлечение архивов
- [ ] Валидация входных данных
- [ ] Ограничение размеров файлов

### Рекомендации

1. Запускайте бота в изолированном окружении
2. Регулярно обновляйте зависимости
3. Мониторьте логи на предмет подозрительной активности
4. Ограничьте права бота до минимально необходимых
