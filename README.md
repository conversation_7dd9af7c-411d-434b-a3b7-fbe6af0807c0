# Plugin Security Bot

Telegram бот на aiogram для автоматической проверки и публикации плагинов в форуме с использованием ИИ анализа безопасности.

## Возможности

- 🔍 **Автоматический анализ безопасности** плагинов с помощью Google Gemini AI
- 🤖 **Интеллектуальная оценка рисков** по 5-уровневой шкале
- 📋 **Автоматическая публикация** безопасных плагинов в топик форума
- ⚠️ **Модерация опасных плагинов** с подтверждением администраторов
- 🧹 **Автоматическая очистка** старых плагинов из топика
- 👥 **Панель администратора** с полным контролем

## Установка

### 1. Клонирование репозитория

```bash
git clone <repository-url>
cd plugin-security-bot
```

### 2. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 3. Настройка переменных окружения

Скопируйте `.env.example` в `.env` и заполните необходимые данные:

```bash
cp .env.example .env
```

Отредактируйте `.env`:

```env
# Токен Telegram бота (получить у @BotFather)
BOT_TOKEN=your_bot_token_here

# API ключ Google Gemini (получить в Google AI Studio)
GEMINI_API_KEY=your_gemini_api_key_here

# ID форума (чата) где находится топик Plugins
FORUM_CHAT_ID=-1001234567890

# ID топика "Plugins" в форуме
PLUGINS_TOPIC_ID=123

# ID администраторов (через запятую)
ADMIN_IDS=123456789,987654321
```

### 4. Получение необходимых данных

#### Токен бота:
1. Напишите @BotFather в Telegram
2. Создайте нового бота командой `/newbot`
3. Скопируйте полученный токен

#### API ключ Gemini:
1. Перейдите в [Google AI Studio](https://aistudio.google.com/)
2. Создайте новый API ключ
3. Скопируйте ключ

#### ID форума и топика:
1. Добавьте бота в ваш форум как администратора
2. Дайте боту права на отправку сообщений и удаление сообщений
3. Получите ID чата (можно через @userinfobot)
4. Получите ID топика "Plugins"

#### ID администраторов:
1. Получите ваш Telegram ID (можно через @userinfobot)
2. Добавьте ID всех администраторов через запятую

## Запуск

```bash
python plugin_security_bot.py
```

## Использование

### Для пользователей

1. **Отправка плагина**: Отправьте файл с расширением `.plugin` боту
2. **Добавьте описание** в подпись к файлу в стандартном формате:

```
🇷🇺 [RU]:
Название: AI Assistant
Автор: @mihailkotovski & @mishabotov
Описание: Универсальный ИИ помощник на базе Google Gemini для ответов на любые вопросы.
Использование: .ai [вопрос] (команду можно изменить в настройках) или включить режим без команд.
Настройки: ✅
Обновлено: 03.08.2025

🇺🇸 [EN]:
Name: AI Assistant
Author: @mihailkotovski & @mishabotov
Description: Universal AI assistant powered by Google Gemini for answering any questions.
Usage: .ai [question] (command can be changed in settings) or enable no-command mode.
Settings: ✅
Updated: 03.08.2025
```

3. **Ожидание результата**: Бот автоматически проанализирует плагин и:
   - Опубликует безопасные плагины сразу
   - Отправит опасные плагины на модерацию администраторам

### Для администраторов

#### Команды:
- `/admin` - Панель администратора
- `/status` - Проверка статуса бота и настроек
- `/cleanup` - Ручная очистка старых плагинов
- `/pending` - Список плагинов, ожидающих проверки

#### Модерация:
- При получении опасного плагина администраторы получают уведомление
- Доступны кнопки "Одобрить" и "Отклонить"
- Пользователь получает уведомление о решении

## Уровни безопасности

Бот использует 5-уровневую систему оценки рисков:

1. **✅ Безопасно** - Плагин использует только стандартные API
2. **❔ Низкий риск** - Незначительные недочеты
3. **⚠️ Осторожно** - Подозрительные действия
4. **📛 Высокий риск** - Серьезная угроза приватности
5. **❌ Опасно** - Явный вредоносный код (требует подтверждения админа)

## Автоматическая очистка

Бот автоматически удаляет старые плагины из топика:
- Проверка каждый час
- Удаление плагинов старше 24 часов
- Можно настроить интервалы в `forum_manager.py`

## Структура проекта

```
plugin-security-bot/
├── plugin_security_bot.py    # Основной файл бота
├── forum_manager.py          # Менеджер работы с форумом
├── requirements.txt          # Зависимости
├── .env.example             # Пример конфигурации
└── README.md               # Документация
```

## Требования

- Python 3.8+
- Telegram бот с правами администратора в форуме
- Google Gemini API ключ
- Форум Telegram с включенными топиками

## Поддержка

При возникновении проблем:
1. Проверьте правильность настройки переменных окружения
2. Убедитесь, что бот имеет необходимые права в форуме
3. Проверьте логи бота для диагностики ошибок
4. Используйте команду `/status` для проверки настроек

## Лицензия

MIT License
